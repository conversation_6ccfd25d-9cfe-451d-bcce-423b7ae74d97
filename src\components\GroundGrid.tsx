
import React, { useState, useCallback } from 'react';
import { InventoryItemStack } from 'src/Interfaces';
import { getText } from 'src/i18n';
import { useRootStore } from 'src/stores/rootStore';
import { ItemsGrid } from './Inventory/ItemsGrid';
import {
  DndContext,
  useSensor,
  useSensors,
  MouseSensor,
  TouchSensor,
  DragStartEvent,
  DragEndEvent,
  DragOverlay,
  rectIntersection,
  CollisionDetection,
} from "@dnd-kit/core";
import { snapCenterToCursor } from '@dnd-kit/modifiers';
import { InventoryItem } from './Inventory/InventoryItem';
import { useGroundStore } from 'src/stores/groundStore';
import { ItemDescriptionPopover } from './ItemDescriptionPopover';
import { ItemLocation } from 'src/enums/common_enum';
import { useLocationStore } from 'src/stores/locationStore';
import { BuildBtn } from './ResourcesPanel';
import { startProgressBar } from 'src/gameinfo';
import { FreshnessFill } from './Inventory/FreshnessFill';

// Fix collision detection for proper drag highlighting
export const fixCursorSnapOffset: CollisionDetection = (args) => {
  // Bail out if keyboard activated
  if (!args.pointerCoordinates) {
    return rectIntersection(args);
  }
  const { x, y } = args.pointerCoordinates;
  const { width, height } = args.collisionRect;
  const updated = {
    ...args,
    // The collision rectangle is broken when using snapCenterToCursor. Reset
    // the collision rectangle based on pointer location and overlay size.
    collisionRect: {
      width,
      height,
      bottom: y + height / 2,
      left: x - width / 2,
      right: x + width / 2,
      top: y - height / 2,
    },
  };
  return rectIntersection(updated);
};


export const GroundPanel = (props: {
  currentLocationBuildingUuid: string
}) => {

  console.log("GroundPanel rendered", props.currentLocationBuildingUuid);
  
  const currRegionIndex = useRootStore(state => state.currRegionIndex);
  const groundBuildings = useGroundStore(state => state.allRegionGroundBuildings[currRegionIndex]);

  const setCurrentLocationBuildingUuid = useLocationStore(state => state.setCurrentLocationBuildingUuid);

  if (!props.currentLocationBuildingUuid) {
    return <div id="inventoryDisplay">
      <GroundBuildingsGridWrapper/>
      <GroundStorageGridWrapper/>
    </div>
  }

  const currentResidingBuilding = groundBuildings.find(stack => stack.uuid === props.currentLocationBuildingUuid);

  const swapBuildings = (fromIndex: number, toIndex: number) => {
    const tmp = currentResidingBuilding.buildingsInside[fromIndex];
    currentResidingBuilding.buildingsInside[fromIndex] = currentResidingBuilding.buildingsInside[toIndex];
    currentResidingBuilding.buildingsInside[toIndex] = tmp;
  }

  // const swapItemOnGround = (fromIndex: number, toIndex: number) => {
  //   const tmp = currentResidingBuilding.itemsInGround[fromIndex];
  //   currentResidingBuilding.itemsInGround[fromIndex] = currentResidingBuilding.itemsInGround[toIndex];
  //   currentResidingBuilding.itemsInGround[toIndex] = tmp;
  // }

  if (currentResidingBuilding) {
    return (
      <div id="inventoryDisplay">
        <GroundBuildingsGrid
          itemStacks={currentResidingBuilding.buildingsInside}
          swapStacks={swapBuildings}
          title={currentResidingBuilding.itemDef.name}
          exit={() => {
            startProgressBar(getText('Exiting'), 2, () => {
              setCurrentLocationBuildingUuid(null);
            });
          }}
        />
        {/* <GroundStorageGrid
          itemStacks={currentResidingBuilding.itemsInGround}
          swapStacks={swapItemOnGround}
          title={getText('GROUND ITEMS')}
        /> */}
      </div>
    )
  }

  return (
    <div id="inventoryDisplay">
      <GroundBuildingsGridWrapper/>
      <GroundStorageGridWrapper/>
    </div>
  )
}


export const GroundBuildingsGridWrapper = () => {
  const currRegionIndex = useRootStore(state => state.currRegionIndex);
  const groundBuildings = useGroundStore(state => state.allRegionGroundBuildings[currRegionIndex]);
  const swapGroundBuildings = useGroundStore(state => state.swapGroundBuildings);

  return <GroundBuildingsGrid
            itemStacks={groundBuildings}
            swapStacks={(fromIndex, toIndex) => swapGroundBuildings(currRegionIndex, fromIndex, toIndex)}
            title={getText('GROUND BUILDINGS')}
          />;
}

export const GroundBuildingsGrid = (props:{
  itemStacks: InventoryItemStack[];
  swapStacks: (fromIndex: number, toIndex: number) => void;
  title: string;
  exit?: () => void;
}) => {
  console.log("GroundBuildingsGrid rendered");

  const [ selectedGroundBuilding, setSelectedGroundBuilding ] = React.useState<InventoryItemStack | null>(null);
  const [ selectedBuildingRect, setSelectedBuildingRect] = React.useState<DOMRect | null>(null);
  const [ activeId, setActiveId ] = useState<string | null>(null);

  return (
    <div className="building-grid-panel">
      <div className="subtitle-label">
          <div>
            <img src = "images/ui_icons/home.svg" className="resource-cube-icon" />
            {props.title}
          </div>
          {props.exit && (
            <div className='house-btn-group'>
              <BuildBtn />
              <div
                  className="panel-btn"
                  onClick={props.exit}
              >
                  <img src="images/ui_icons/exit.png" width={14} height={14} />
                  <div className="panel-btn-text">{getText("Exit")}</div>
              </div>
            </div>
          )}
      </div>
      <CommonGrid
        selectedStack={selectedGroundBuilding}
        setSelectedStack={setSelectedGroundBuilding}
        selectedRect={selectedBuildingRect}
        setSelectedRect={setSelectedBuildingRect}
        activeId={activeId}
        setActiveId={setActiveId}
        itemStacks={props.itemStacks}
        swapStacks={props.swapStacks}
        location={props.exit ? ItemLocation.InsideABuilding : ItemLocation.GroundBuilding}
        // gridMaxheight = '88%'
      />
    </div>
  );
};

export const GroundStorageGridWrapper = () => {
  const currRegionIndex = useRootStore(state => state.currRegionIndex);
  const groundStorageStacks = useGroundStore(state => state.allRegionGroundStacks[currRegionIndex]);
  const swapGroundStorageStacks = useGroundStore(state => state.swapStacks);

  return <GroundStorageGrid
            itemStacks={groundStorageStacks}
            swapStacks={(fromIndex, toIndex) => swapGroundStorageStacks(currRegionIndex, fromIndex, toIndex)}
            title={getText('GROUND ITEMS')}
          />;
}

export const GroundStorageGrid = (props:{
  itemStacks: InventoryItemStack[];
  swapStacks: (fromIndex: number, toIndex: number) => void;
  title: string;
}) => {
  const [ selectedGroundStorageStack, setSelectedGroundStorageStack ] = React.useState<InventoryItemStack | null>(null);
  const [ selectedGroundItemRect, setSelectedGroundItemRect] = React.useState<DOMRect | null>(null);
  const [ activeId, setActiveId ] = useState<string | null>(null);

  return (
    <div className="building-grid-panel">
      <div className="subtitle-label">
          <div>
            <img src = "images/ui_icons/home.svg" className="resource-cube-icon" />
            {props.title}
          </div>
      </div>
      <CommonGrid
        selectedStack={selectedGroundStorageStack}
        setSelectedStack={setSelectedGroundStorageStack}
        selectedRect={selectedGroundItemRect}
        setSelectedRect={setSelectedGroundItemRect}
        activeId={activeId}
        setActiveId={setActiveId}
        itemStacks={props.itemStacks}
        swapStacks={props.swapStacks}
        location={ItemLocation.Ground}
        // gridMaxheight = '38%'
      />
    </div>
  );
};


const CommonGrid = ({
  selectedStack,
  setSelectedStack,
  selectedRect,
  setSelectedRect,
  activeId,
  setActiveId,
  itemStacks,
  swapStacks,
  // currRegionIndex,
  location
  // gridMaxheight
}) => {
  console.log("CommonGrid rendered");

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    })
  );

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  }, []);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    console.log('Drag end:', { activeId, overId });

    swapStacks(parseInt(activeId.replace('draggable-', '')), parseInt(overId.replace('droppable-', '')));
  }, [swapStacks]);

  
  const handleItemStackSelection = React.useCallback((itemStack: InventoryItemStack, element?: HTMLElement) => {
      // Store the bounding rectangle of the selected item for popover positioning
      if (element) {
          // Get the bounding rectangle relative to the viewport
          const rect = element.getBoundingClientRect();

          // Set the rect first, then the selected stack to ensure the popover has the position
          // before it tries to render
          setSelectedRect(rect);

          // Use a small timeout to ensure the rect is set before the popover renders
          setTimeout(() => {
              setSelectedStack(itemStack);
          }, 0);
      } else {
          console.warn("No element reference provided for popover positioning");
          setSelectedStack(itemStack);
      }
  }, [selectedStack]);

  
  // Get the currently dragged item for the overlay
  const activeItem: InventoryItemStack | null = activeId ? (() => {
    if (activeId.startsWith('draggable-')) {
      const index = parseInt(activeId.replace('draggable-', ''));
      return itemStacks[index];
    } 
    console.log("Not a ground building", activeId);
    return null;
  })() : null;

  return (
    <>
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        collisionDetection={fixCursorSnapOffset}
      >
          <div
            className="buildings-grid scrollable-container"
            // style={{maxHeight: gridMaxheight ? gridMaxheight : 'none'}}
          >
            <ItemsGrid
              itemStacks={itemStacks}
              selectedStack={selectedStack}
              setSelectedStack={handleItemStackSelection}
              activeId={activeId}
            />
          </div>
          
          {/* Drag Overlay for displaying image at mouse position */}
          <DragOverlay modifiers={[snapCenterToCursor]} dropAnimation={null}>
            {activeItem ? (
              <div
                className='inventory-item-inner'
                style={{background: "none", border: "none", boxShadow: "none"}}
              >
                <FreshnessFill 
                    freshness={activeItem.freshness} totalFreshness={activeItem.itemDef.freshness}
                />
                <div className="drag-overlay-item">
                  <InventoryItem itemStack={activeItem} itemDef={activeItem.itemDef} />
                </div>
              </div>
            ) : null}
          </DragOverlay>
      </DndContext>
      
      {selectedStack && (
          <ItemDescriptionPopover
              selectedStack={selectedStack}
              // itemDef={itemDef}
              anchorRect={selectedRect}
              isOpen={!!selectedStack}
              onClose={() => {
                  setSelectedStack(null);
                  setSelectedRect(null);
              }}
              location={location}
          />)}
      </>
    );
};





// const InventoryItemSlot = React.memo((props: {
//     itemStack: InventoryItemStack,
//     setSelectedStack: (itemStack: InventoryItemStack, element?: HTMLElement) => void,
//     stackIndex: number,
//     isActive: boolean,
//     isSelected: boolean,
// }) => {
//     console.log("InventoryItemSlot rendered!!!!", props.stackIndex, props.isActive);

//     // Create the data object
//     const draggableData = {
//         index: props.stackIndex,
//         itemStack: props.itemStack
//     };

//     // Use the DragDropWrapper to isolate the DnD context changes
//     // Pass isSelected directly to DragDropWrapper so it will re-render when selection changes
//     return (
//         <StorageDragDropWrapper
//             id={`${props.stackIndex}`}
//             data={draggableData}
//             disabled={!props.itemStack}
//             isSelected={props.isSelected}
//             // setSelectedStack={props.setSelectedStack}
//         >
//             {({ ref, isDragging, isOver, attributes, listeners, isSelected }) => (
//                 <InventoryItemContent
//                     itemStack={props.itemStack}
//                     isSelected={isSelected}
//                     setSelectedStack={props.setSelectedStack}
//                     isDragging={isDragging}
//                     isOver={isOver}
//                     attributes={attributes}
//                     listeners={listeners}
//                     innerRef={ref}
//                 />
//             )}
//         </StorageDragDropWrapper>
//     );
// }, (prevProps, nextProps) => {
//     // Custom equality function to prevent unnecessary re-renders
//     if (prevProps.isActive !== nextProps.isActive) return false;
//     if (prevProps.isSelected !== nextProps.isSelected) {
//         console.log("selected changed 000", nextProps.stackIndex, prevProps.isSelected, nextProps.isSelected);
//         return false;
//     }

//     // Check if itemStack has changed
//     if (!prevProps.itemStack && !nextProps.itemStack) return true;
//     if (!prevProps.itemStack || !nextProps.itemStack) return false;

//     return (
//         prevProps.itemStack.uuid === nextProps.itemStack.uuid &&
//         prevProps.itemStack.quantity === nextProps.itemStack.quantity
//     );
// });


