import SimplexNoise from 'simplex-noise';
import Delaunator from 'delaunator';
import Poisson from 'poisson-disk-sampling';
import { generateInteriorBoundaryPoints } from "./dual-mesh/create.js";
import { TriangleMesh } from "./dual-mesh/index.js";
import { WorldMap } from './map.js';
import * as Draw from './draw.js';
import * as Colormap from './colormap.js';
import * as urlUtils from 'url-search-utils';
import { makeRandInt, makeRandFloat } from '@redblobgames/prng';
import {
    initSettingsMenu, realSecToGameMinRatio,
    initAudio,
    mapSize,
    enableFogOfWar
} from './settings.js';
// import { initMinimap, updateMinimap } from './minimap.js';
import { battleSystem } from './battle.js';
// import { getText } from './i18n.js';
// import { initInventorySystem, inventorySystem, spoilInventory, updateInventoryDisplay, updateResourceDisplay } from './inventory.js';
import { gameStatus, initTimeUI, player, updateTimeWithIngameMin } from './gameinfo.js';
import { initWeatherUI } from './weather_system.js';
// import { initFishingSystem, updateFishingButton } from './fishing.js';
// import { initCraftingUI } from './crafting.js';
// import { initEquipmentUI } from './equipment.js';
// import { terrainInfo } from './terrain_info.js';
// import { exploreBiome, updateResourcePanel } from './components/ResourcesPanel.js';
// import { PlacedBuilding } from './Interfaces.js';
// import { createRoot } from 'react-dom/client.js';
import { GameRootPage } from "./components/GameRootPage.js";
import React = require('react');
import { createRoot } from 'react-dom/client';
import { FogOfWar } from './fogOfWar';
import { showTooltip } from './util.js';
import { getText } from './i18n.js';
// import { initBuildingSystemWithSwitcher } from './BuildingSystemSwitcher.tsx';
// import { initBuildingSystem } from './test';

/** @import { Item } from './resources.js' */

// Initialize settings menu
initSettingsMenu();

// Fog of war instance
let fogOfWar: FogOfWar | null = null;


let resolvePromise;
const imgLoadPromise = new Promise((resolve, reject) => {
    resolvePromise = resolve; // Resolve the Promise on success
});

interface UIState {
    seed: number;
    variant: number;
    size: 'tiny' | 'small' | 'medium' | 'large' | 'huge';
    'noisy-fills': boolean;
    'noisy-edges': boolean;
    icons: boolean;
    biomes: boolean;
    lighting: boolean;
    'north-temperature': number;
    'south-temperature': number;
    rainfall: number;
    canvasSize: number;
    persistence: number;
    temperature?: number; // For backwards compatibility
}

let defaultUiState: UIState = {
    seed: 187,
    variant: 0,
    // size: 'medium',
    size: 'large',
    'noisy-fills': true,
    'noisy-edges': true,
    icons: true,
    biomes: true,
    lighting: true,
    'north-temperature': 0,
    'south-temperature': 0,
    rainfall: 0,
    canvasSize: 0,
    persistence: 0, /* 0 means "normal" persistence value of 1/2 */
};

export const uiState: UIState = {} as UIState;
Object.assign(uiState, defaultUiState);

/** Generate boundary points around the edges of the map.

  I normally recommend using generateInteriorBoundaryPoints()
  but for the public version of mapgen2, changing the boundary
  point algorithm makes the maps not match previously generated
  maps. So I am using the older boundary point function here to
  maintain map output compatibility.
 */
function compat_addBoundaryPoints(spacing, size) {
    let N = Math.ceil(size / spacing);
    let points = [];
    for (let i = 0; i <= N; i++) {
        let t = (i + 0.5) / (N + 1);
        let w = size * t;
        let offset = Math.pow(t - 0.5, 2);
        points.push([offset, w], [size - offset, w]);
        points.push([w, offset], [w, size - offset]);
    }
    return points;
}
const COMPATIBILITY_WITH_OLD_MAP_SHAPES = true;

let _mapCache: WorldMap[] = [];
const spacing = {
    tiny: 38,
    small: 26,
    medium: 18,
    large: 12.8,
    huge: 9,
};

// the larger this value, the clearer (higher resolution) icons are
const browserWorldRatio = 1.5;

// World settings
const world = {
    // width: mapSize[uiState.size],
    // height: mapSize[uiState.size]
    width: mapSize[uiState.size] * browserWorldRatio,
    height: mapSize[uiState.size] * browserWorldRatio
};

const baseWorldSize = 1000;

for (const key in spacing) {
    spacing[key] = spacing[key] * baseWorldSize / 1000;
}

/**
 *
 * @param {string} size
 * @returns {WorldMap}
 */
function getMap(size: UIState['size']) {
    if (!_mapCache[size]) {
        // NOTE: the seeds here are constant so that I can reuse the same
        // mesh and noisy edges for all maps, but you could get more variety
        // by creating a new TriangleMesh object each time
        const bounds = { left: 0, top: 0, width: baseWorldSize, height: baseWorldSize };
        let points = COMPATIBILITY_WITH_OLD_MAP_SHAPES
            ? compat_addBoundaryPoints(spacing[size], baseWorldSize)
            : generateInteriorBoundaryPoints(bounds, spacing[size]);
        let numBoundaryPoints = points.length;
        let generator = new Poisson({
            shape: [bounds.width, bounds.height],
            minDistance: spacing[size],
        }, makeRandFloat(12345));
        for (let p of points) { generator.addPoint(p); }
        points = generator.fill();

        let init = { points, delaunator: Delaunator.from(points), numBoundaryPoints };
        init = TriangleMesh.addGhostStructure(init);
        let mesh = new TriangleMesh(init);

        _mapCache[size] = new WorldMap(
            new TriangleMesh(mesh),
            { amplitude: 0.2, length: 4, seed: 12345 },
            makeRandInt
        );
        console.log(`Map size "${size}" has ${_mapCache[size].mesh.numRegions} regions`);
    }
    return _mapCache[size];
}


/**
 * Manage drawing with requestAnimationFrame
 *
 * 1. Each frame call one function from the queue.
 * 2. If the queue empties, stop calling requestAnimationFrame.
 */
const processingPerFrameInMs = 1000 / 60;
let requestAnimationFrameId = null;
let requestAnimationFrameQueue = [];
function requestAnimationFrameHandler() {
    requestAnimationFrameId = null;
    let timeStart = performance.now();
    while (requestAnimationFrameQueue.length > 0
        && performance.now() - timeStart < processingPerFrameInMs) {
        let f = requestAnimationFrameQueue.shift();
        f();
    }
    console.log("requestAnimationFrameQueue.length", requestAnimationFrameQueue.length);
    if (requestAnimationFrameQueue.length > 0) {
        requestAnimationFrameId = requestAnimationFrame(requestAnimationFrameHandler);
    }
    if (requestAnimationFrameQueue.length == 0) {
        resolvePromise();
        console.log("initMap done");
    }
}


/* map icons */
// const mapIconsConfig = { left: 9, top: 4, filename: "map-icons.png" };
interface MapIconsConfig {
    left: number;
    top: number;
    filename: string;
    image?: HTMLImageElement;
}

const mapIconsConfig: MapIconsConfig = { left: 90, top: 0, filename: "map-icons.svg" };
mapIconsConfig.image = new Image();
if (mapIconsConfig.image) {
    mapIconsConfig.image.onload = initMap;
    mapIconsConfig.image.src = mapIconsConfig.filename;
}

let _lastUiState: Partial<UIState> = {};


// Create off-screen canvas for background
const bgCanvas = document.createElement("canvas");
const ctx = bgCanvas.getContext("2d")!;

// Create move button
const moveButtons = document.querySelector('#move-buttons-container');
moveButtons.querySelector("#moveButtonText").textContent = getText("Move");
moveButtons.querySelector("#cancelMoveButtonText").textContent = getText("Cancel Move");



function initMap() {
    console.trace("initMap");

    // world.width = mapSize[uiState.size];
    // world.height = mapSize[uiState.size];
    console.log("uiState.size ", uiState.size);
    console.log("world size ", world.width, world.height);

    bgCanvas.width = world.width;
    bgCanvas.height = world.height;
    console.log("bgCanvas.width ", bgCanvas.width);
    console.log("bgCanvas.height ", bgCanvas.height);

    // Update fog of war world size if it exists, or reset if map changed
    if (fogOfWar) {
        fogOfWar.updateWorldSize(world.width, world.height);
        // Reset exploration when map changes (new seed/variant)
        if (_lastUiState.seed !== uiState.seed || _lastUiState.size !== uiState.size) {
            // fogOfWar.resetExploration();
            // Re-reveal area around player if player has spawned
            if (player.hasSpawned) {
                fogOfWar.updateExploration(player.x + player.width / 2, player.y + player.height / 2);
            }
        }
    }


    let map = getMap(uiState.size);
    let noisyEdges = true;// false; // uiState['noisy-edges'];
    let noisyFills = uiState['noisy-fills'];

    // let canvas = document.getElementById('map');
    // let ctx = canvas.getContext('2d');

    // let size = Math.min(canvas.parentNode.clientWidth, canvas.parentNode.clientHeight);
    // if (size != uiState.canvasSize) {
    //     // Don't assign to width,height if the size hasn't changed because
    //     // it will blank out the canvas and we'd like to reuse the previous drawMap
    //     uiState.canvasSize = size;
    //     canvas.style.width = size + 'px';
    //     canvas.style.height = size + 'px';
    //     size = 1024;
    //     if (window.devicePixelRatio && window.devicePixelRatio != 1) {
    //         size *= window.devicePixelRatio;
    //     }
    //     canvas.width = size;
    //     canvas.height = size;
    // }

    let SNoise = SimplexNoise as any; // Temp workaround for import issue
    let noise = new SNoise(makeRandFloat(uiState.seed));
    let persistence = Math.pow(1 / 2, 1 + uiState.persistence);
    let islandShapeAmplitudes = Array.from({ length: 5 }, (_, octave) => Math.pow(persistence, octave));
    let biomeBias = {
        north_temperature: uiState['north-temperature'],
        south_temperature: uiState['south-temperature'],
        moisture: uiState.rainfall,
    };
    let colormap = uiState.biomes ? new Colormap.Discrete() : new Colormap.Smooth();
    let queue = [];
    if ((!noisyEdges || uiState.size === 'large' || uiState.size === 'huge')
        && (_lastUiState.seed !== uiState.seed
            || _lastUiState.size !== uiState.size
            || _lastUiState.canvasSize !== uiState.canvasSize)) {
        // Noisy edges are slow enough that it'd be nice to have a
        // quick approximation drawn first, but if the last time we
        // drew something was with the same essential parameters let's
        // reuse the drawing from last time
        queue.push(() => Draw.approximateIslandShape(ctx, baseWorldSize, baseWorldSize, noise, { round: 0.5, inflate: 0.4, amplitudes: islandShapeAmplitudes.slice(0, 3) }));
        // TODO: the if() test is too convoluted; rewrite that expression
    }
    Object.assign(_lastUiState, uiState);

    queue.push(
        () => map.calculate({
            noise: noise,
            drainageSeed: uiState.variant,
            riverSeed: uiState.variant,
            biomeBias: biomeBias,
            shape: { round: 0.5, inflate: 0.4, amplitudes: islandShapeAmplitudes },
        }),
        () => {
            Draw.background(ctx!, colormap);
            Draw.noisyRegions(ctx!, map, colormap, noisyEdges);
            // Draw the rivers early for better user experience
            Draw.rivers(ctx!, map, colormap, noisyEdges, true);
        }
    );

    for (let phase = 0; phase < 16; phase++) {
        queue.push(() => Draw.noisyEdges(ctx!, map, colormap, noisyEdges, phase));
    }

    // Have to draw the rivers and coastlines again because the noisy
    // edges might overwrite them, and these should take priority over
    // the other noisy edges. Otherwise it leaves little gaps that look
    // ugly when zoomed in.
    queue.push(() => Draw.rivers(ctx!, map, colormap, noisyEdges, false));
    queue.push(() => Draw.coastlines(ctx!, map, colormap, noisyEdges));

    if (noisyFills) {
        queue.push(() => Draw.noisyFill(ctx!, baseWorldSize, baseWorldSize, makeRandInt(12345)));
    }

    if (uiState.icons) {
        queue.push(() => Draw.regionIcons(ctx!, map, mapIconsConfig as any, makeRandInt(uiState.variant)));
    }

    if (uiState.lighting) {
        queue.push(() => Draw.lighting(ctx!, baseWorldSize, baseWorldSize, map));
    }

    requestAnimationFrameQueue = queue.map(
        (layer, i) => () => {
            //console.time("layer "+i);
            ctx!.save();
            console.log("ctx.scale(world.width / baseWorldSize, world.height / 1000)",
                world.width / baseWorldSize,
                world.height / baseWorldSize);
            ctx!.scale(world.width / baseWorldSize, world.height / baseWorldSize);
            layer();
            ctx!.restore();
            //console.timeEnd("layer "+i);
        });

    if (!requestAnimationFrameId) {
        requestAnimationFrameId = requestAnimationFrame(requestAnimationFrameHandler);
    }

}


const exploredRegions = new Set();

// Function to find a random valid spawn point on land
function findRandomSpawnPoint(map: WorldMap): { x: number, y: number } {
    const validRegions: number[] = [];
    
    // Collect all valid land regions (not water/ocean)
    for (let r = 0; r < map.mesh.numRegions; r++) {
        if (!map.water_r[r] && !map.ocean_r[r]) {
            validRegions.push(r);
        }
    }
    
    if (validRegions.length === 0) {
        console.warn("No valid land regions found for spawn!");
        return { x: world.width / 2, y: world.height / 2 }; // Fallback to center
    }
    
    // Pick a random valid region
    const randomRegion = validRegions[Math.floor(Math.random() * validRegions.length)];
    exploredRegions.add(randomRegion);
    
    // Get the region center coordinates (in 0-1000 range)
    const regionX = map.mesh.x_of_r(randomRegion);
    const regionY = map.mesh.y_of_r(randomRegion);
    
    // Convert to world coordinates
    return {
        x: (regionX / baseWorldSize) * world.width,
        y: (regionY / baseWorldSize) * world.height
    };
}

function initGameUI() {
    setTimeout(async () => {

        // initMinimap();

        // Create status bars container
        const statusContainer = document.getElementById('statusContainer');

        console.log("11111111111111111");

        gameStatus.element = statusContainer;


        initTimeUI();

        // Initialize weather system
        initWeatherUI();

        initAudio();

        // Initialize battle system
        battleSystem.init(document.body);
        

        // wait for the map to init
        await imgLoadPromise;

        console.log("imgLoadPromise resolved");
        
        // Set initial player position when map is first created
        if (!player.hasSpawned) {
            const map = getMap(uiState.size);
            const spawnPoint = findRandomSpawnPoint(map);
            player.x = spawnPoint.x - player.width / 2; // Center player on spawn point
            player.y = spawnPoint.y - player.height / 2;
            player.hasSpawned = true;

            // Center camera on spawn point
            const desiredCameraX = player.x - (canvas.width / camera.zoom) / 2 + player.width / 2;
            const desiredCameraY = player.y - (canvas.height / camera.zoom) / 2 + player.height / 2;

            // Apply boundary constraints
            const constrainedPos = applyCameraBounds(desiredCameraX, desiredCameraY);
            camera.x = constrainedPos.x;
            camera.y = constrainedPos.y;

            // Initialize fog of war
            if (enableFogOfWar) {
                fogOfWar = new FogOfWar(world.width, world.height);
                // Reveal initial area around player
                fogOfWar.updateExploration(player.x + player.width / 2, player.y + player.height / 2);
            }
        }
        updateTerrainInfo();
        drawAll();
    }, 0);
}


function initUi() {
    function oninput(element: HTMLInputElement) { element.addEventListener('input', getUiState); }
    function onclick(element: HTMLInputElement) { element.addEventListener('click', getUiState); }
    function onchange(element: HTMLInputElement) { element.addEventListener('change', getUiState); }
    document.querySelectorAll<HTMLInputElement>("input[type='radio']").forEach(onclick);
    document.querySelectorAll<HTMLInputElement>("input[type='checkbox']").forEach(onclick);
    document.querySelectorAll<HTMLInputElement>("input[type='number']").forEach(onchange);
    document.querySelectorAll<HTMLInputElement>("input[type='range']").forEach(oninput);

    // HACK: on touch devices use touch event to make the slider feel better
    document.querySelectorAll<HTMLInputElement>("input[type='range']").forEach((slider) => {
        function handleTouch(e: TouchEvent) {
            let rect = slider.getBoundingClientRect();
            let min = parseFloat(slider.getAttribute('min')!),
                max = parseFloat(slider.getAttribute('max')!),
                step = parseFloat(slider.getAttribute('step')!) || 1;
            let value = (e.changedTouches[0].clientX - rect.left) / rect.width;
            value = min + value * (max - min);
            value = Math.round(value / step) * step;
            if (value < min) { value = min; }
            if (value > max) { value = max; }
            slider.value = value.toString();
            slider.dispatchEvent(new Event('input'));
            e.preventDefault();
            e.stopPropagation();
        };
        slider.addEventListener('touchmove', handleTouch, { passive: true });
        slider.addEventListener('touchstart', handleTouch, { passive: true });
    });
}

function setUiState() {
    (document.getElementById('seed') as HTMLInputElement).value = uiState.seed.toString();
    (document.getElementById('variant') as HTMLInputElement).value = uiState.variant.toString();
    (document.querySelector("input#size-" + uiState.size) as HTMLInputElement).checked = true;
    (document.querySelector("input#noisy-edges") as HTMLInputElement).checked = uiState['noisy-edges'];
    (document.querySelector("input#noisy-fills") as HTMLInputElement).checked = uiState['noisy-fills'];
    (document.querySelector("input#icons") as HTMLInputElement).checked = uiState.icons;
    (document.querySelector("input#biomes") as HTMLInputElement).checked = uiState.biomes;
    (document.querySelector("input#lighting") as HTMLInputElement).checked = uiState.lighting;
    (document.querySelector("input#north-temperature") as HTMLInputElement).value = uiState['north-temperature'].toString();
    (document.querySelector("input#south-temperature") as HTMLInputElement).value = uiState['south-temperature'].toString();
    (document.querySelector("input#rainfall") as HTMLInputElement).value = uiState.rainfall.toString();
    (document.querySelector("input#persistence") as HTMLInputElement).value = uiState.persistence.toString();
}

function getUiState() {
    uiState.seed = (document.getElementById('seed') as HTMLInputElement).valueAsNumber;
    uiState.variant = (document.getElementById('variant') as HTMLInputElement).valueAsNumber;
    uiState.size = (document.querySelector("input[name='size']:checked") as HTMLInputElement).value as UIState['size'];
    uiState['noisy-edges'] = (document.querySelector("input#noisy-edges") as HTMLInputElement).checked;
    uiState['noisy-fills'] = (document.querySelector("input#noisy-fills") as HTMLInputElement).checked;
    uiState.icons = (document.querySelector("input#icons") as HTMLInputElement).checked;
    uiState.biomes = (document.querySelector("input#biomes") as HTMLInputElement).checked;
    uiState.lighting = (document.querySelector("input#lighting") as HTMLInputElement).checked;
    uiState['north-temperature'] = (document.querySelector("input#north-temperature") as HTMLInputElement).valueAsNumber;
    uiState['south-temperature'] = (document.querySelector("input#south-temperature") as HTMLInputElement).valueAsNumber;
    uiState.rainfall = (document.querySelector("input#rainfall") as HTMLInputElement).valueAsNumber;
    uiState.persistence = (document.querySelector("input#persistence") as HTMLInputElement).valueAsNumber;
    setUrlFromState();
    initMap();
}

function setSeed(seed) {
    uiState.seed = seed & 0x7fffffff;
    setUiState();
    getUiState();
}

function setVariant(variant) {
    uiState.variant = ((variant % 10) + 10) % 10;
    setUiState();
    getUiState();
}

(window as any).prevSeed = function () { setSeed(uiState.seed - 1); };
(window as any).nextSeed = function () { setSeed(uiState.seed + 1); };
(window as any).prevVariant = function () { setVariant(uiState.variant - 1); };
(window as any).nextVariant = function () { setVariant(uiState.variant + 1); };


let _setUrlFromStateTimeout = null;
function _setUrlFromState() {
    _setUrlFromStateTimeout = null;
    let fragment = urlUtils.stringifyParams(uiState, {}, {
        'canvasSize': 'exclude',
        'noisy-edges': 'include-if-falsy',
        'noisy-fills': 'include-if-falsy',
    });
    let url = window.location.pathname + "#" + fragment;
    window.history.replaceState({}, null, url);
    document.getElementById('url').setAttribute('href', window.location.href);
}
function setUrlFromState() {
    // Rate limit the url update because some browsers (like Safari
    // iOS) throw an error if you change the url too quickly.
    if (_setUrlFromStateTimeout === null) {
        _setUrlFromStateTimeout = setTimeout(_setUrlFromState, 500);
    }
}

function getStateFromUrl() {
    const bool = (value) => value === 'true';
    let hashState = urlUtils.parseQuery(
        window.location.hash.slice(1),
        {
            'seed': 'number',
            'variant': 'number',
            'noisy-edges': bool,
            'noisy-fills': bool,
            'icons': bool,
            'biomes': bool,
            'lighting': bool,
            'north-temperature': 'number',
            'south-temperature': 'number',
            'rainfall': 'number',
            'persistence': 'number',
        }
    );
    Object.assign(uiState, defaultUiState);
    Object.assign(uiState, hashState);
    if (hashState.temperature) {
        // backwards compatibility -- I changed the url from
        // &temperature= to separate north and south parameters
        uiState['north-temperature'] = uiState.temperature;
        uiState['south-temperature'] = uiState.temperature;
        delete uiState.temperature;
    }
    setUrlFromState();
    // setUiState();
}
window.addEventListener('hashchange', getStateFromUrl);
window.addEventListener('resize', initMap);


console.log("window.innerWidth", window.innerWidth, window.innerHeight);

// Camera object
const camera = {
    x: 0,
    y: 0,
    zoom: 1, // Zoom level, 1 is default (no zoom)
    minZoom: window.innerWidth / world.width, // Minimum zoom level. The smaller the current zoom level is, the laggier it gets
    maxZoom: 1.2, // Maximum zoom level
    isDragging: false, // Flag to track if the user is dragging the map
    dragStartX: 0, // Raw clientX from mousedown for delta calculation
    dragStartY: 0, // Raw clientY from mousedown for delta calculation
    cameraStartX: 0, // Camera X position when dragging started
    cameraStartY: 0 // Camera Y position when dragging started
};

/**
 * Apply boundary constraints to camera position to prevent empty space in viewport
 * @param x - Desired camera x position
 * @param y - Desired camera y position
 * @returns Constrained camera position
 */
function applyCameraBounds(x: number, y: number): { x: number, y: number } {
    const viewportWidth = canvas.width / camera.zoom;
    const viewportHeight = canvas.height / camera.zoom;

    let constrainedX = x;
    let constrainedY = y;

    // If the map is smaller than the viewport in any dimension, center it
    if (world.width <= viewportWidth) {
        constrainedX = (world.width - viewportWidth) / 2;
    } else {
        // Clamp camera position to prevent empty space
        // Left boundary: camera.x >= 0 (no empty space on left)
        // Right boundary: camera.x + viewportWidth <= world.width (no empty space on right)
        // Therefore: camera.x <= world.width - viewportWidth
        constrainedX = Math.max(0, Math.min(world.width - viewportWidth, x));
    }

    if (world.height <= viewportHeight) {
        constrainedY = (world.height - viewportHeight) / 2;
    } else {
        // Clamp camera position to prevent empty space
        // Top boundary: camera.y >= 0 (no empty space on top)
        // Bottom boundary: camera.y + viewportHeight <= world.height (no empty space on bottom)
        // Therefore: camera.y <= world.height - viewportHeight
        constrainedY = Math.max(0, Math.min(world.height - viewportHeight, y));
    }

    // Debug logging can be enabled if needed
    // console.log("applyCameraBounds DEBUG:");
    // console.log("  Input:", { x, y });
    // console.log("  Output:", { x: constrainedX, y: constrainedY });
    // console.log("  Viewport:", { width: viewportWidth, height: viewportHeight });
    // console.log("  World:", { width: world.width, height: world.height });
    // console.log("  Zoom:", camera.zoom);
    // console.log("  Canvas:", { width: canvas.width, height: canvas.height });
    // console.log("  Constraints X:", { min: 0, max: world.width - viewportWidth });
    // console.log("  Constraints Y:", { min: 0, max: world.height - viewportHeight });

    return { x: constrainedX, y: constrainedY };
}

// Movement object to track click-to-move state
const movement = {
    totalInGameMin: 0,
    moving: false,
    targetSet: false, // Flag to indicate if a target has been set but movement hasn't started
    targetX: 0,
    targetY: 0,
    // moveButtons: null as { x: number, y: number, width: number, height: number } | null, // Will store the coordinates and dimensions of the move button
    lastBiomeIsWater: false,
    closestRegionAtTarget: null
};

/**
 * Main game canvas for rendering the game world
 * @type {HTMLCanvasElement}
 */
const canvas = document.getElementById("gameCanvas") as HTMLCanvasElement;
const activeCtx = canvas.getContext("2d")!;

// Set canvas size
canvas.width = window.innerWidth; // camera.width;
canvas.height = window.innerHeight; //camera.height;

// Enable image smoothing for better zoomed rendering
activeCtx.imageSmoothingEnabled = true;
activeCtx.imageSmoothingQuality = 'high';



initUi();
// Initialize terrain info after windows are set up
initGameUI();
// getStateFromUrl();
initMap();
setUiState();



// Game interaction state
let isLeftMouseButtonDown = false;
let potentialClickEventData: { clientX: number, clientY: number, worldX: number, worldY: number } | null = null;
const DRAG_THRESHOLD = 5; // pixels

const cancelMove = () => {
    movement.moving = false;
    movement.targetSet = false; // Reset target set flag
    movement.closestRegionAtTarget = null;
    moveButtons.style.display = 'none'; // Hide move button
};

// Update fog of war and explored regions as player moves
const updateExploration = () => {
    // console.time("updateExploration");
    let alreadyExplored = false;
    if (fogOfWar) {
        alreadyExplored = fogOfWar.updateExploration(player.x + player.width / 2, player.y + player.height / 2);
    }
    if (!alreadyExplored) {
        const map = getMap(uiState.size);
        // Convert player position to map coordinates (0-1000 range)
        const mapX = player.x / fakeCoordsToRealWorldRatio;
        const mapY = player.y / fakeCoordsToRealWorldRatio;
        const closestRegion = map.findRegionAt(mapX, mapY);
        exploredRegions.add(closestRegion);
    }
    // console.timeEnd("updateExploration");
};

function update(deltaSec) {

    let inGameMin;
    // Handle click-to-move
    if (movement.moving) {
        // Calculate direction to target
        const dx = movement.targetX - player.x;
        const dy = movement.targetY - player.y;
        const remainingDistance = Math.sqrt(dx * dx + dy * dy);

        inGameMin = deltaSec * realSecToGameMinRatio;
        const movementThisFrame = player.speed * inGameMin;
        // console.log("remainingDistance", remainingDistance, movementThisFrame);
        // console.log("movementThisFrame", movementThisFrame);

        if (remainingDistance > movementThisFrame) {
            
            const map = getMap(uiState.size);
            // Convert player position to map coordinates (0-1000 range)
            const mapX = player.x / fakeCoordsToRealWorldRatio;
            const mapY = player.y / fakeCoordsToRealWorldRatio;


            // TODO: this is for the water boat system
            // // Use the new efficient lookup method
            // const closestRegion = map.findRegionAt(mapX, mapY);

            // const biome = map.biome_r[closestRegion];
            // const isWater = map.water_r[closestRegion];

            // if (isWater && !movement.lastBiomeIsWater) {
            //     console.log("moving", closestRegion, biome, isWater);
            //     updateTerrainInfo(true, false);
            //     movement.moving = false;
            //     return;
            // } else if (gameStatus.hasBoat && !isWater && movement.lastBiomeIsWater) {
            //     // Boat is not allowed to move on land
            //     console.log("moving", closestRegion, biome, isWater);
            //     updateTerrainInfo(false, true);
            //     movement.moving = false;
            //     return;
            // }
            // movement.lastBiomeIsWater = isWater;




            // movement.totalInGameMin += inGameMin;

            // Move towards target
            const angle = Math.atan2(dy, dx);
            const xMov = Math.cos(angle) * movementThisFrame;
            const yMov = Math.sin(angle) * movementThisFrame;

            // console.log("Movement", xMov, yMov, "dx:", dx, "dy:", dy, "remainingDistance:", remainingDistance);
            player.x += xMov;
            player.y += yMov;

            updateExploration();

            // inGameMin = deltaSec * realSecToGameMinRatio
        } else {
            console.log("Arrived at target before the frame ended.", remainingDistance, movementThisFrame);
            player.x = movement.targetX;
            player.y = movement.targetY;

            cancelMove();        
            inGameMin = remainingDistance / player.speed;

            // Update fog of war when arriving at destination
            updateExploration();

            console.log("Arrived at target", player.x, player.y, movement.targetX, movement.targetY);

            updateTerrainInfo();
            // movement.totalInGameMin += inGameMin;
            // movement.totalInGameMin = 0;
        }
        // totalMins += inGameMin;
        // console.log("totalMins", totalMins);

        // Keep player within bounds
        player.x = Math.max(0, Math.min(world.width - player.width, player.x));
        player.y = Math.max(0, Math.min(world.height - player.height, player.y));

        // // Camera follows player, accounting for zoom
        // camera.x = player.x - (canvas.width / camera.zoom) / 2 + player.width / 2;
        // camera.y = player.y - (canvas.height / camera.zoom) / 2 + player.height / 2;

        // // Keep camera within bounds
        // camera.x = Math.max(0, Math.min(world.width - canvas.width / camera.zoom, camera.x));
        // camera.y = Math.max(0, Math.min(world.height - canvas.height / camera.zoom, camera.y));


        // console.time("updateTimeWithIngameMin");
        updateTimeWithIngameMin(inGameMin);
        // console.timeEnd("updateTimeWithIngameMin");
        drawAll();
    } else if (movement.targetSet) {
        // If target is set but not moving, just draw to show the dashed line and move button
        drawAll();
    }
}


const locationImg = new Image();
locationImg.onerror = function (e) {
  console.error('Image load error:', e);
};
locationImg.src = 'images/ui_icons/location.png';

let alreadyPrinted = false;

// browser canvas world size: world.width; real word size: 1000 * 1000
const fakeCoordsToRealWorldRatio = world.width / 1000;

/**
 * Coordinate transformation helpers for zoom-aware rendering
 * These functions handle the conversion between different coordinate systems:
 * - World coordinates: absolute positions in the game world
 * - Camera coordinates: world coordinates relative to camera position
 * - Screen coordinates: final pixel positions on canvas after zoom transformation
 */

/**
 * Convert world coordinates to screen coordinates for zoom-aware rendering
 * This accounts for camera position and zoom level while maintaining proper center-based zoom
 */
function worldToScreen(worldX: number, worldY: number): { x: number, y: number } {
    // Convert to camera-relative coordinates
    const cameraRelativeX = worldX - camera.x;
    const cameraRelativeY = worldY - camera.y;

    // Apply zoom transformation from viewport center
    // This simulates the center-based zoom transformation without affecting camera coordinates
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    // Transform coordinates as if zoom is applied from center
    const screenX = centerX + (cameraRelativeX - centerX) * camera.zoom;
    const screenY = centerY + (cameraRelativeY - centerY) * camera.zoom;

    return { x: screenX, y: screenY };
}

/**
 * Convert screen coordinates to world coordinates (inverse of worldToScreen)
 * Used for mouse interactions and click-to-world coordinate conversion
 */
function screenToWorld(screenX: number, screenY: number): { x: number, y: number } {
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    // Reverse the zoom transformation
    const cameraRelativeX = centerX + (screenX - centerX) / camera.zoom;
    const cameraRelativeY = centerY + (screenY - centerY) / camera.zoom;

    // Convert back to world coordinates
    const worldX = cameraRelativeX + camera.x;
    const worldY = cameraRelativeY + camera.y;

    return { x: worldX, y: worldY };
}

function drawAll() {
    // console.time("drawAll()");

    // clear the whole canvas
    activeCtx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background and basic elements with simple zoom scaling
    // This maintains compatibility with existing camera coordinate system
    activeCtx.save();
    activeCtx.scale(camera.zoom, camera.zoom);

    // Draw the pre-rendered background with zoom applied
    activeCtx.drawImage(bgCanvas, -camera.x, -camera.y);

    // Draw the player relative to the camera with zoom applied
    activeCtx.fillStyle = player.color;
    activeCtx.fillRect(player.x - camera.x, player.y - camera.y, player.width, player.height);

    activeCtx.restore();

    // Draw visual overlays using coordinate transformation helpers
    // This provides proper center-based zoom behavior for UI elements

    // Draw fog of war overlay using simple zoom scaling (compatible with existing system)
    if (fogOfWar) {
        activeCtx.save();
        activeCtx.scale(camera.zoom, camera.zoom);
        fogOfWar.renderFog(activeCtx, camera.x, camera.y);
        activeCtx.restore();
    }

    // Draw visual overlays using coordinate transformation helpers for proper center-based zoom
    if (movement.targetSet) {
        // Convert world coordinates to screen coordinates using our transformation helpers
        const playerWorldPos = { x: player.x + player.width / 2, y: player.y + player.height / 2 };
        const targetWorldPos = { x: movement.targetX, y: movement.targetY };

        const playerScreenPos = worldToScreen(playerWorldPos.x, playerWorldPos.y);
        const targetScreenPos = worldToScreen(targetWorldPos.x, targetWorldPos.y);

        if (!alreadyPrinted) {
            console.log("camera.x, camera.y", camera.x, camera.y);
            console.log("playerWorldPos", playerWorldPos);
            console.log("playerScreenPos", playerScreenPos);
            console.log("targetWorldPos", targetWorldPos);
            console.log("targetScreenPos", targetScreenPos);
        }

        // Draw dashed line using screen coordinates (no additional transformations needed)
        activeCtx.save();
        activeCtx.beginPath();
        activeCtx.setLineDash([8, 4]);
        activeCtx.lineDashOffset = -((Date.now() / 100) % 12);
        activeCtx.moveTo(playerScreenPos.x, playerScreenPos.y);
        activeCtx.lineTo(targetScreenPos.x, targetScreenPos.y);
        activeCtx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        activeCtx.lineWidth = 2;
        activeCtx.stroke();
        activeCtx.setLineDash([]);
        activeCtx.restore();

        // Draw navigation marker at destination using screen coordinates
        activeCtx.save();
        const markerSize = 20;
        activeCtx.translate(targetScreenPos.x, targetScreenPos.y);
        activeCtx.drawImage(locationImg, -10, -21, markerSize, markerSize);
        activeCtx.restore();

        // // Draw a pulsing circle
        // const pulseScale = 1 + 0.2 * Math.sin(Date.now() / 200); // Pulsing effect
        // activeCtx.scale(pulseScale, pulseScale);
        
        // // Outer circle
        // activeCtx.beginPath();
        // activeCtx.arc(0, 0, markerSize/2, 0, Math.PI * 2);
        // activeCtx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
        // activeCtx.lineWidth = 2;
        // activeCtx.stroke();

        // // Inner circle
        // activeCtx.beginPath();
        // activeCtx.arc(0, 0, markerSize/4, 0, Math.PI * 2);
        // activeCtx.fillStyle = 'white';
        // activeCtx.fill();
        // activeCtx.drawImage(locationImg, -16, -26, markerSize, markerSize); markerSize = 30

        // draw a hexagon for the movement block region sides
        const map = getMap(uiState.size);
        let closestRegionAtTarget = movement.closestRegionAtTarget;
        if (!closestRegionAtTarget) {
            console.log("Unexpected Error: closestRegionAtTarget is null, finding closest region at target");
            const targetInWorldCoords = {
                x: movement.targetX / fakeCoordsToRealWorldRatio,
                y: movement.targetY / fakeCoordsToRealWorldRatio
            };
            closestRegionAtTarget = map.findRegionAt(targetInWorldCoords.x, targetInWorldCoords.y);
        }

        const biome = map.biome_r[closestRegionAtTarget];
        const regionSides = map.mesh.s_around_r(closestRegionAtTarget);

        if (!alreadyPrinted) {
            console.log("movement", movement.targetX, movement.targetY);
            console.log("targetScreenPos", targetScreenPos);
            console.log("closestRegion2, biome", closestRegionAtTarget, biome);
            // console.log("regionSides", regionSides);
        }

        // Draw hexagon region boundary overlays using coordinate transformation helpers
        for (let s of regionSides) {
            let last_t = map.mesh.t_inner_s(s);
            let first_t = map.mesh.t_outer_s(s);

            // Convert mesh coordinates to world coordinates
            const p1World = {
                x: map.mesh.x_of_t(last_t) * fakeCoordsToRealWorldRatio,
                y: map.mesh.y_of_t(last_t) * fakeCoordsToRealWorldRatio
            };
            const p2World = {
                x: map.mesh.x_of_t(first_t) * fakeCoordsToRealWorldRatio,
                y: map.mesh.y_of_t(first_t) * fakeCoordsToRealWorldRatio
            };

            // Convert to screen coordinates using our transformation helpers
            const p1Screen = worldToScreen(p1World.x, p1World.y);
            const p2Screen = worldToScreen(p2World.x, p2World.y);

            // if (!alreadyPrinted ) {
            //     console.log("regionSides x y of t",
            //         map.mesh.x_of_t(last_t), map.mesh.y_of_t(last_t),
            //         map.mesh.x_of_t(first_t), map.mesh.y_of_t(first_t));
            // }

            activeCtx.save();

            activeCtx.beginPath();
            activeCtx.setLineDash([8, 4]); // Adjust dash pattern for better visual effect
            activeCtx.lineDashOffset = -((Date.now() / 100) % 12); // Animate the dashes to flow towards destination
            activeCtx.moveTo(p1.x, p1.y);
            activeCtx.lineTo(p2.x, p2.y);
            activeCtx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            activeCtx.lineWidth = 4;
            activeCtx.stroke();
            activeCtx.setLineDash([]);

            activeCtx.restore();
        }

        alreadyPrinted = true;
    }

    // go back to the first restore (the one after the canvas is cleared)
    activeCtx.restore();
    
    // console.timeEnd("drawAll()");
}

const minSecBetweenFrames = 1 / 60;
let previousMs;
const gameLoop = (endMsOfPrevFrame) => {
    if (previousMs === undefined) {
        previousMs = endMsOfPrevFrame;
    }

    let deltaSec = (endMsOfPrevFrame - previousMs) / 1000;
    if (deltaSec >= minSecBetweenFrames) {
        // console.log("gameLoop", deltaSec, endMsOfPrevFrame, previousMs);
        if (deltaSec > 0.5) {
            console.log("gameLoop > 0.5 sec", deltaSec, endMsOfPrevFrame, previousMs);
        }
        update(deltaSec);
        deltaSec = 0;
    }
    // while (deltaSec >= minSecBetweenFrames) {
    //     if (deltaSec > 0.5) {
    //         console.log("gameLoop", deltaSec, endMsOfPrevFrame, previousMs);
    //         // console.time("update");
    //         // update(deltaSec);
    //         // console.timeEnd("update");
    //         // deltaSec -= minSecBetweenFrames;
    //         // continue;
    //     }
    //     update(minSecBetweenFrames);
    //     deltaSec -= minSecBetweenFrames;
    // }

    // If updated, then use the new endMsOfPrevFrame, otherwise use the previous endMsOfPrevFrame.
    previousMs = endMsOfPrevFrame - deltaSec * 1000; // Make sure we don't lose unprocessed (delta) time
    requestAnimationFrame(gameLoop);
}


console.log("drawAll000");
requestAnimationFrame(gameLoop); // Start the game loop



let reactRoot = null;

function updateTerrainInfo(isFromLandToWater: boolean = false, isFromWaterToLand: boolean = false) {
    console.time("updateTerrainInfo");

    const map = getMap(uiState.size);
    // Convert player position to map coordinates (0-1000 range)
    const playerRealCoords = {
        x: player.x / fakeCoordsToRealWorldRatio,
        y: player.y / fakeCoordsToRealWorldRatio
    };
    // const mapX = Math.floor((player.x / world.width) * 1000);
    // const mapY = Math.floor((player.y / world.height) * 1000);

    // Use the new efficient lookup method
    const closestRegion = map.findRegionAt(playerRealCoords.x, playerRealCoords.y);

    const biome = map.biome_r[closestRegion];
    const water = map.water_r[closestRegion];
    console.log("updateTerrainInfo",closestRegion, biome, water, map.t_river.some(x => x==closestRegion));




    let s_out = [];
    // get side index of the region
    map.mesh.s_around_r(closestRegion, s_out);
    let river_s = null;
    for (let s of s_out) {
        // flow_s[s] > 0 means this side is a river
        if (map.flow_s[s] > 0 || map.flow_s[map.mesh.s_opposite_s(s)] > 0) {
            console.log("hasriver=", s, map.flow_s[s], map.flow_s[map.mesh.s_opposite_s(s)]);
            river_s = s;
            break;
        };
    }
    console.log("s_out", playerRealCoords.x, playerRealCoords.y, s_out);
    console.log("terrainInfo;; r=", closestRegion + ";;; biome=" + biome + ";;; hasRiver=" + (river_s!=null));
    if (river_s) {
        // get the 2 points of a side
        let last_t = map.mesh.t_inner_s(river_s);
        console.log("last_t", map.mesh.x_of_t(last_t), map.mesh.y_of_t(last_t));
        let first_t = map.mesh.t_outer_s(river_s);
        console.log("first_t", map.mesh.x_of_t(first_t), map.mesh.y_of_t(first_t));
        const Ax = map.mesh.x_of_t(first_t);
        const Ay = map.mesh.y_of_t(first_t);
        const Bx = map.mesh.x_of_t(last_t);
        const By = map.mesh.y_of_t(last_t);

        const distance = getDistance(Ax, Ay, Bx, By, playerRealCoords.x, playerRealCoords.y);
        console.log("distance", distance);
        if (distance < 7) {
            console.log("close to river");
        }
    }


    if (!reactRoot) {
        const reactRootDOM = document.querySelector('#react-root');
        reactRoot = createRoot(reactRootDOM);
        console.log("initializing reactRoot...");
    }
    reactRoot.render(
        <GameRootPage
            regionIndex={closestRegion}
            resourceMetaInfosInAllRegions={map.resources_r}
            biomesList={map.biome_r}
            isFromLandToWater={isFromLandToWater}
            isFromWaterToLand={isFromWaterToLand}
        />
    );

    console.timeEnd("updateTerrainInfo");
}

// calculate the distance from point C to line segment AB
function getDistance(Ax, Ay, Bx, By, Cx, Cy) {
    const numerator = Math.abs(
        (Bx - Ax) * (Ay - Cy) - (Ax - Cx) * (By - Ay)
    );
    // Calculate denominator (length of line segment AB)
    const denominator = Math.sqrt(
        Math.pow(Bx - Ax, 2) + Math.pow(By - Ay, 2)
    );

    // Avoid division by zero (if points A and B are identical)
    if (denominator === 0) {
        return Math.sqrt(
            Math.pow(Cx - Ax, 2) + Math.pow(Cy - Ay, 2)
        );
    }
    return numerator / denominator;
}


// Handle canvas click
canvas.addEventListener("mousedown", (e) => {
    const rect = canvas.getBoundingClientRect();
    const mousedownClientX = e.clientX - rect.left;
    const mousedownClientY = e.clientY - rect.top;

    if (e.button === 0) { // Left click
        isLeftMouseButtonDown = true;
        
        // Store for potential click processing on mouseup
        potentialClickEventData = {
            clientX: mousedownClientX,
            clientY: mousedownClientY,
            // Calculate world coords using CORRECTED formula for this mousedown point
            worldX: ((mousedownClientX - canvas.width/2) / camera.zoom) + canvas.width/2 + camera.x,
            worldY: ((mousedownClientY - canvas.height/2) / camera.zoom) + canvas.height/2 + camera.y
        };

        // Store for potential drag processing in mousemove
        camera.dragStartX = e.clientX; // Raw clientX from event for delta calculation
        camera.dragStartY = e.clientY; // Raw clientY from event for delta calculation
        camera.cameraStartX = camera.x;
        camera.cameraStartY = camera.y;
        camera.isDragging = false; // Not dragging yet

        e.preventDefault(); // Prevent text selection, default image dragging etc.

    } else if (e.button === 2) { // Right click 
        e.preventDefault(); // Keep preventing context menu
    }
});


let lastDragRenderedPos = { x: 0, y: 0 };
let dragRenderPending = false;

// Handle mouse move for dragging
canvas.addEventListener("mousemove", (e) => {
    if (!isLeftMouseButtonDown) {
        return;
    }

    if (!camera.isDragging) { // Check if we should START dragging
        // dx/dy from the original mousedown event's clientX/Y (stored in camera.dragStartX/Y)
        const dxMouse = e.clientX - camera.dragStartX;
        const dyMouse = e.clientY - camera.dragStartY;
        if (Math.sqrt(dxMouse*dxMouse + dyMouse*dyMouse) > DRAG_THRESHOLD) {
            camera.isDragging = true;
            potentialClickEventData = null; // It's a drag, not a click
        }
    }

    if (camera.isDragging && !dragRenderPending) {

        const dx = e.clientX - camera.dragStartX; // Delta from mousedown position
        const dy = e.clientY - camera.dragStartY;

        // Calculate new camera position
        const newCameraX = camera.cameraStartX - dx / camera.zoom;
        const newCameraY = camera.cameraStartY - dy / camera.zoom;

        // Apply boundary constraints
        const clampedCameraPos = applyCameraBounds(newCameraX, newCameraY);
        camera.x = clampedCameraPos.x;
        camera.y = clampedCameraPos.y;

        // Throttle rendering using requestAnimationFrame
        dragRenderPending = true;
        setTimeout(() => {
            // console.time("dragRender");
            const startTime = performance.now();
            // const startTime = new Date().getTime(); 
            drawAll();
            const endTime = performance.now(); // new Date().getTime();
            const timeTaken = endTime - startTime;
            if (timeTaken > 0.3) {
                console.log("dragRender took large than 0.3s", timeTaken);
            }

            // console.timeEnd("dragRender");
            dragRenderPending = false;
        }, 0);
    }
    e.preventDefault(); // Prevent default actions during mouse move when button is down
});

const move = () => {
    if (movement.targetSet && !movement.moving) {
        movement.moving = true;
        // moveButtons.style.display = 'none';
        drawAll();
    }
}

// Handle mouse up to stop dragging or process click
canvas.addEventListener("mouseup", function(e) {
    if (e.button === 0) { // Left mouse button up
        isLeftMouseButtonDown = false;
        if (camera.isDragging) {
            camera.isDragging = false;
        } else if (potentialClickEventData) { // It was a click


            const clickData = potentialClickEventData;
            
            // Click was on map, set target
            // Use worldX, worldY from clickData (already calculated with corrected formula)
            movement.targetX = clickData.worldX; // - player.width / 2;
            movement.targetY = clickData.worldY; // - player.height / 2;
            
            // Apply bounds for targetX, targetY to ensure player's top-left is within world limits
            movement.targetX = Math.max(0, Math.min(world.width - player.width, movement.targetX));
            movement.targetY = Math.max(0, Math.min(world.height - player.height, movement.targetY));

            
            const map = getMap(uiState.size);
            // convert to low level real coordinates
            const targetInWorldCoords = {
                x: movement.targetX / fakeCoordsToRealWorldRatio,
                y: movement.targetY / fakeCoordsToRealWorldRatio
            };

            const closestRegionAtTarget = map.findRegionAt(targetInWorldCoords.x, targetInWorldCoords.y);
            movement.closestRegionAtTarget = closestRegionAtTarget;
            const biome = map.biome_r[closestRegionAtTarget];
            const posOfR = map.mesh.pos_of_r(closestRegionAtTarget);
            console.log("closestRegionAtTarget !!!!!!!!!!!", movement, posOfR);
            // convert back
            movement.targetX = posOfR[0] * fakeCoordsToRealWorldRatio;
            movement.targetY = posOfR[1] * fakeCoordsToRealWorldRatio;

            showTooltip(
                e,
                this, 
                `<div class='selected-resource-header'>${exploredRegions.has(closestRegionAtTarget) ? getText(biome) : "?????"}</div>
                <div class="resource-stats">${getText("Temperature: ")}</div>
                <div class="resource-stats">${getText("Humidity: ")}</div>
                <div class='panel-btn'>
                    <div class="icon-img-div" style="max-width: 14px; max-height: 14px;">
                        <img class="icon-img" src="images/ui_icons/navigation.png">
                    </div>
                    ${getText("Move Here")}
                </div>`,
                {
                    left: clickData.clientX,
                    top: clickData.clientY,
                    width: 50,
                    height: 50,
                    bottom: clickData.clientY,
                    right: clickData.clientX
                },
                move
            );


            movement.targetSet = true;
            alreadyPrinted = false;
            moveButtons.style.display = 'flex'; // show move button
            movement.moving = false; // Don't start moving yet. If already moving, stop moving.

            console.log("Target set (click): X=", movement.targetX, "Y=", movement.targetY);
            drawAll(); // Redraw to show target line and move button
        }
        potentialClickEventData = null; // Ensure cleared
    }
});


// Handle mouse wheel for zooming
canvas.addEventListener("wheel", (e) => {
    e.preventDefault();

    // Store the current viewport center in world coordinates before zoom
    const oldViewportWidth = canvas.width / camera.zoom;
    const oldViewportHeight = canvas.height / camera.zoom;
    const oldViewportCenterX = camera.x + oldViewportWidth / 2;
    const oldViewportCenterY = camera.y + oldViewportHeight / 2;

    // Adjust zoom level based on wheel direction
    const zoomFactor = 0.1;
    if (e.deltaY < 0) {
        // Zoom in
        camera.zoom = Math.min(camera.maxZoom, camera.zoom * (1 + zoomFactor));
    } else {
        // Zoom out
        camera.zoom = Math.max(camera.minZoom, camera.zoom / (1 + zoomFactor));
    }

    // Calculate new viewport dimensions after zoom
    const newViewportWidth = canvas.width / camera.zoom;
    const newViewportHeight = canvas.height / camera.zoom;

    // Try to keep the same world center point in the viewport center
    let newCameraX = oldViewportCenterX - newViewportWidth / 2;
    let newCameraY = oldViewportCenterY - newViewportHeight / 2;

    // Apply boundary constraints to prevent empty space
    const constrainedPos = applyCameraBounds(newCameraX, newCameraY);
    camera.x = constrainedPos.x;
    camera.y = constrainedPos.y;

    drawAll();
});

// Handle mouse leave to stop dragging if mouse leaves canvas
canvas.addEventListener("mouseleave", () => {
    if (isLeftMouseButtonDown) { // If mouse was down and leaves canvas
        isLeftMouseButtonDown = false;
        if (camera.isDragging) {
            camera.isDragging = false;
        }
        potentialClickEventData = null; // Clear potential click if mouse leaves
    }
});

moveButtons.querySelector("#moveBtn").addEventListener('click', move);
moveButtons.querySelector("#cancelMoveBtn").addEventListener('click', () => {
    if (movement.targetSet) {
        cancelMove();
        drawAll();
    }
});

// disable right click globally
document.addEventListener('contextmenu', event => event.preventDefault());




// document.addEventListener('click', (e) => {
//     const img = document.getElementById('location-img');
//     img.style.display = 'block';

//     // const rect = container.getBoundingClientRect();
//     const x = e.clientX;// - rect.left;
//     const y = e.clientY; // - rect.top;
//     console.log("click", e.clientX, e.clientY);
//     // Set position based on click
//     img.style.left = `${x - 15}px`;
//     img.style.top = `${y - 25}px`;
// })

const statusBars = document.querySelector(".status-bars");
statusBars.querySelector(".health").addEventListener('click', function(event) {
    showTooltip(event, this, getText('When Health drops to 0, you are dead'));
});
statusBars.querySelector(".food").addEventListener('click', function(event) {
    showTooltip(event, this, getText('When food drops to 0, you start to lose fat'));
});
statusBars.querySelector(".energy").addEventListener('click', function(event) {
    showTooltip(event, this, getText('You lose more energy at night. Recover energy by sleeping.'));
});
statusBars.querySelector(".fat").addEventListener('click', function(event) {
    showTooltip(event, this, getText('Gain fat by eating more than you need.'));
});
statusBars.querySelector(".sanity").addEventListener('click', function(event) {
    showTooltip(event, this, getText('You will lose more sanity at night and when your energy drops to 0.'));
});

statusBars.querySelector(".weight-bar").addEventListener('click', function(event) {
    showTooltip(event, this, getText('100'));
});

