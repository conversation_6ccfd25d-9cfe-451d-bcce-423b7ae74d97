import { InventoryItemStack, Item } from "src/Interfaces";
import { mapSize } from "src/settings";
import { create } from "zustand";
import { v4 as uuidv4 } from 'uuid';
import { addStacksToOtherStacks, reduceQuantityWithShallowCopy, spoilItemStacks } from "src/util";
import { useRootStore } from "src/stores/rootStore";
import { useLocationStore } from "./locationStore";

interface GroundStore {
    allRegionGroundStacks: InventoryItemStack[][];

    spoilGroundStacks: (inGameMin: number) => void;

    addOneToGroundStacks: (regionIndex: number, itemDef: Item) => void;
    addItemDefsToGroundStacks: (regionIndex: number, itemDefs: Item[]) => void; // preferred

    // addStackToGroundStacks: (regionIndex: number, itemStack: InventoryItemStack) => void;
    addStacksToGroundStacks: (regionIndex: number, itemStacks: InventoryItemStack[]) => void; // preferred
    removeStackByIndex: (regionIndex: number, stackIndex: number) => void;
    removeStackByUuid: (uuid: string, quantity?: number) => void;
    swapStacks: (regionIndex: number, fromIndex: number, toIndex: number) => void;
    addStackByIndex: (regionIndex: number, stackIndex: number, itemStack: InventoryItemStack) => void;

    
    allRegionGroundBuildings: InventoryItemStack[][];

    addToGroundBuildings: (regionIndex: number, itemDef: Item) => void;
    // addItemDefsToGroundBuildings: (regionIndex: number, itemDefs: Item[]) => void; // preferred
    swapGroundBuildings: (regionIndex: number, fromIndex: number, toIndex: number) => void;
    addStackToABuilding: (itemStack: InventoryItemStack) => void;
    removeStackFromABuilding: (stackUuid: string, quantity?: number) => void;
}

export const useGroundStore = create<GroundStore>((set, get) => ({
    allRegionGroundStacks: new Array(mapSize['medium']).fill(null).map(() => new Array(5)),
    allRegionGroundBuildings: new Array(mapSize['medium']).fill(null).map(() => new Array(5)),

    spoilGroundStacks: (inGameMin) => set((state) => {
        const currRegionIndex = useRootStore.getState().currRegionIndex;
        const newStacks = spoilItemStacks(state.allRegionGroundStacks[currRegionIndex], inGameMin);

        if (newStacks) {
            const allRegionGroundStacks_Copy = [...state.allRegionGroundStacks];
            allRegionGroundStacks_Copy[currRegionIndex] = newStacks;
            return { allRegionGroundStacks: allRegionGroundStacks_Copy };
        }

        return {}; // No change if nothing updated
    }),

    addStackToABuilding: (itemStack: InventoryItemStack) => set((state) => {
        const regionIndex = useRootStore.getState().currRegionIndex;
        const buildingUuid = useLocationStore.getState().currentLocationBuildingUuid;
        const allRegionGroundBuildings_Copy = [...state.allRegionGroundBuildings];
        const groundBuildings = allRegionGroundBuildings_Copy[regionIndex];
        const groundBuildings_Copy = [...groundBuildings];
        allRegionGroundBuildings_Copy[regionIndex] = groundBuildings_Copy;
        const buildingIndex = groundBuildings_Copy.findIndex(stack => stack?.uuid === buildingUuid);
        if (buildingIndex === -1) {
            return { allRegionGroundBuildings: allRegionGroundBuildings_Copy };
        }
        const building_Copy = {...groundBuildings_Copy[buildingIndex]};
        
        addStacksToOtherStacks([itemStack], building_Copy.buildingsInside);

        groundBuildings_Copy[buildingIndex] = building_Copy;

        return { allRegionGroundBuildings: allRegionGroundBuildings_Copy };
    }),

    removeStackFromABuilding: (stackUuid: string, quantity: number) => set((state) => {
        const regionIndex = useRootStore.getState().currRegionIndex;
        const buildingUuid = useLocationStore.getState().currentLocationBuildingUuid;
        const allRegionGroundBuildings_Copy = [...state.allRegionGroundBuildings];
        const groundBuildings = allRegionGroundBuildings_Copy[regionIndex];
        const groundBuildings_Copy = [...groundBuildings];
        allRegionGroundBuildings_Copy[regionIndex] = groundBuildings_Copy;
        const buildingIndex = groundBuildings_Copy.findIndex(stack => stack?.uuid === buildingUuid);
        if (buildingIndex === -1) {
            return { allRegionGroundBuildings: allRegionGroundBuildings_Copy };
        }
        const building_Copy = {...groundBuildings_Copy[buildingIndex]};

        const stackIndex = building_Copy.buildingsInside.findIndex(stack => stack?.uuid === stackUuid);
        const buildingsInside_Copy = [...building_Copy.buildingsInside];
        if (quantity) {
            if (building_Copy.buildingsInside[stackIndex].quantity < quantity) {
                console.error("Unexpected error! removeStackFromABuilding quantity > stack.quantity");
                return { allRegionGroundBuildings: allRegionGroundBuildings_Copy };
            }

            reduceQuantityWithShallowCopy(buildingsInside_Copy, stackIndex, quantity);
        } else {
            buildingsInside_Copy[stackIndex] = null;
        }
        building_Copy.buildingsInside = buildingsInside_Copy;

        groundBuildings_Copy[buildingIndex] = building_Copy;

        return { allRegionGroundBuildings: allRegionGroundBuildings_Copy };
    }),

    addToGroundBuildings: (regionIndex: number, itemDef: Item) => set((state) => {
        const allRegionGroundBuildings_Copy = [...state.allRegionGroundBuildings];
        const groundBuildings = allRegionGroundBuildings_Copy[regionIndex];
        const itemStack = {
            itemId: itemDef.id,
            quantity: 1,
            uuid: uuidv4(),
            itemDef: itemDef,
            buildingsInside: itemDef.canHaveBuildings? new Array(5).fill(null) : null,
            itemsInGround: itemDef.canHaveBuildings? new Array(5).fill(null) : null,
        };
        if (groundBuildings == null) {
            allRegionGroundBuildings_Copy[regionIndex] = [itemStack];
        } else {
            //  try to find an empty slot
            let foundEmptySlot = false;
            for (let i = 0; i < groundBuildings.length; i++) {
                if (!groundBuildings[i]) {
                    groundBuildings[i] = itemStack;
                    allRegionGroundBuildings_Copy[regionIndex] = [...groundBuildings];
                    foundEmptySlot = true;
                    break;
                }
            }
            if (!foundEmptySlot) {
                allRegionGroundBuildings_Copy[regionIndex] = [...groundBuildings, itemStack];
            }
        }
        return { allRegionGroundBuildings: allRegionGroundBuildings_Copy };
    }),
    

    // addItemDefsToGroundBuildings: (regionIndex: number, itemDefs: Item[]) => {
    //     const { addStacksToGroundStacks } = get();

    //     const itemIdAndStackMap: Map<string, InventoryItemStack> = new Map();
    //     itemDefs.forEach((itemDef) => {
    //         const addQuantity = itemDef.quantityMod ? itemDef.quantityMod : 1;
    //         if (itemIdAndStackMap.has(itemDef.id)) {
    //             itemIdAndStackMap.get(itemDef.id).quantity += addQuantity;
    //             return;
    //         } else {
    //             itemIdAndStackMap.set(itemDef.id, {
    //                 itemId: itemDef.id,
    //                 freshness: itemDef.freshness,
    //                 quantity: addQuantity,
    //                 uuid: uuidv4(),
    //                 itemDef: itemDef
    //             });
    //         }
    //     });
    //     addStacksToGroundStacks(regionIndex, [...itemIdAndStackMap.values()]);
    // },

    swapGroundBuildings: (regionIndex: number, fromIndex: number, toIndex: number) => set((state) => {
        const allRegionGroundBuildings_Copy = [...state.allRegionGroundBuildings];
        const newGroundBuildings = [...allRegionGroundBuildings_Copy[regionIndex]];
        const temp = newGroundBuildings[fromIndex];
        newGroundBuildings[fromIndex] = newGroundBuildings[toIndex];
        newGroundBuildings[toIndex] = temp;
        allRegionGroundBuildings_Copy[regionIndex] = newGroundBuildings;
        
        return { allRegionGroundBuildings: allRegionGroundBuildings_Copy };
    }),

    // initializeRegion: (regionIndex) => set((state) => {
    //     const allRegionGroundStacks = [];
    //     return { allRegionGroundStacks: newPlacedBuildingsInAllRegions };
    // }),

    addOneToGroundStacks: (regionIndex: number, itemDef: Item) => {
        const { addStackToGroundStacks } = get();
        
        const itemStack: InventoryItemStack = {
            itemId: itemDef.id,
            freshness: itemDef.freshness,
            quantity: itemDef.quantityMod ? itemDef.quantityMod : 1,
            uuid: uuidv4(),
            itemDef: itemDef
        };
        addStackToGroundStacks(regionIndex, itemStack);
    },

    addItemDefsToGroundStacks: (regionIndex: number, itemDefs: Item[]) => {
        const { addStacksToGroundStacks } = get();

        const itemIdAndStackMap: Map<string, InventoryItemStack> = new Map();
        itemDefs.forEach((itemDef) => {
            const addQuantity = itemDef.quantityMod ? itemDef.quantityMod : 1;
            if (itemIdAndStackMap.has(itemDef.id)) {
                itemIdAndStackMap.get(itemDef.id).quantity += addQuantity;
                return;
            } else {
                itemIdAndStackMap.set(itemDef.id, {
                    itemId: itemDef.id,
                    freshness: itemDef.freshness,
                    quantity: addQuantity,
                    uuid: uuidv4(),
                    itemDef: itemDef
                });
            }
        });
        addStacksToGroundStacks(regionIndex, [...itemIdAndStackMap.values()]);
    },

    // addStackToGroundStacks: (regionIndex: number, itemStack: InventoryItemStack) => set((state) => {
    //     const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
    //     const groundStacks = newAllRegionGroundStacks[regionIndex];
    //     if (groundStacks == null) {
    //         newAllRegionGroundStacks[regionIndex] = [itemStack];
    //     } else {
    //         //  try to find an empty slot
    //         let foundEmptySlot = false;
    //         for (let i = 0; i < groundStacks.length; i++) {
    //             if (!groundStacks[i]) {
    //                 groundStacks[i] = itemStack;
    //                 newAllRegionGroundStacks[regionIndex] = [...groundStacks];
    //                 foundEmptySlot = true;
    //                 break;
    //             }
    //         }
    //         if (!foundEmptySlot) {
    //             newAllRegionGroundStacks[regionIndex] = [...groundStacks, itemStack];
    //         }
    //     }
    //     return { allRegionGroundStacks: newAllRegionGroundStacks };
    // }),

    addStacksToGroundStacks: (regionIndex: number, itemStacks: InventoryItemStack[]) => set((state) => {
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const groundStacks = newAllRegionGroundStacks[regionIndex];

        if (groundStacks == null || groundStacks.length === 0) { // if groundStacks is null or empty, just add all stacks to the end
            newAllRegionGroundStacks[regionIndex] = [...itemStacks];
            return { allRegionGroundStacks: newAllRegionGroundStacks };
        }
            
        const groundStacksCopy = [...groundStacks]; // copy to avoid mutating original array


        addStacksToOtherStacks(itemStacks, groundStacksCopy);
   
        newAllRegionGroundStacks[regionIndex] = groundStacksCopy; // set the new array to the region index in the newAllRegionGroundStacks array
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    addStackByIndex: (regionIndex: number, stackIndex: number, itemStack: InventoryItemStack) => set((state) => {
        
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const newGroundStacks = [...newAllRegionGroundStacks[regionIndex]];
        newGroundStacks[stackIndex] = itemStack;
        newAllRegionGroundStacks[regionIndex] = newGroundStacks;
        
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    removeStackByIndex: (regionIndex: number, stackIndex: number) => set((state) => {
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const newGroundStacks = [...newAllRegionGroundStacks[regionIndex]];
        newAllRegionGroundStacks[regionIndex] = newGroundStacks.splice(stackIndex, 1);
        
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    removeStackByUuid: (uuid: string, quantity: number = null) => set((state) => {
        const currRegionIndex = useRootStore.getState().currRegionIndex;

        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const newGroundStacks = [...newAllRegionGroundStacks[currRegionIndex]];
        const stackIndex = newGroundStacks.findIndex(stack => stack?.uuid === uuid);
        if (quantity) {
            if (newGroundStacks[stackIndex].quantity < quantity) {
                console.error("Unexpected error! removeStackByUuid quantity > stack.quantity");
                return { allRegionGroundStacks: newAllRegionGroundStacks };
            }

            reduceQuantityWithShallowCopy(newGroundStacks, stackIndex, quantity);
        } else {
            newGroundStacks[stackIndex] = null;
        }
        newAllRegionGroundStacks[currRegionIndex] = newGroundStacks;
        
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    swapStacks: (regionIndex: number, fromIndex: number, toIndex: number) => set((state) => {
        const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
        const newGroundStacks = [...newAllRegionGroundStacks[regionIndex]];
        const temp = newGroundStacks[fromIndex];
        newGroundStacks[fromIndex] = newGroundStacks[toIndex];
        newGroundStacks[toIndex] = temp;
        newAllRegionGroundStacks[regionIndex] = newGroundStacks;
        
        return { allRegionGroundStacks: newAllRegionGroundStacks };
    }),

    // tryConsumeItemsFromGroundStacks: (regionIndex: number, itemsToRemove: ItemToConsume[]) => {
    //     let success = false;
        
    //     set((state) => {
    //         const newAllRegionGroundStacks = [...state.allRegionGroundStacks];
    //         const groundStacks = newAllRegionGroundStacks[regionIndex];
    //         if (groundStacks == null) {
    //             return { allRegionGroundStacks: newAllRegionGroundStacks };
    //         } else {
    //             const newGroundStacks = groundStacks.filter(stack => !itemStacks.find(itemStack => itemStack.uuid === stack.uuid));
    //             newAllRegionGroundStacks[regionIndex] = newGroundStacks;
    //             return { allRegionGroundStacks: newAllRegionGroundStacks };
    //         }
    //     })
    // },
}));
