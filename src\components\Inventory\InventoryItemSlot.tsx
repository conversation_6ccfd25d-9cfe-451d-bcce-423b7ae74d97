import React = require("react");
import { InventoryItemStack } from "src/Interfaces";
import { DragDropWrapper } from "../DragDropWrapper";
import { InventoryItemContent } from "./InventoryItemContent";

export const InventoryItemSlot = React.memo((props: {
    itemStack: InventoryItemStack,
    setSelectedStack: (itemStack: InventoryItemStack, element?: HTMLElement) => void,
    stackIndex: number,
    isActive: boolean,
    isSelected: boolean,
}) => {
    console.log("InventoryItemSlot rendered!!!!", props.stackIndex, props.isActive);

    // Create the data object
    const draggableData = {
        index: props.stackIndex,
        itemStack: props.itemStack
    };

    // Use the DragDropWrapper to isolate the DnD context changes
    // Pass isSelected directly to DragDropWrapper so it will re-render when selection changes
    return (
        <DragDropWrapper
            id={`${props.stackIndex}`}
            data={draggableData}
            disabled={!props.itemStack}
            isSelected={props.isSelected}
            // setSelectedStack={props.setSelectedStack}
        >
            {({ ref, isDragging, isOver, attributes, listeners, isSelected }) => (
                <InventoryItemContent
                    itemStack={props.itemStack}
                    isSelected={isSelected}
                    setSelectedStack={props.setSelectedStack}
                    isDragging={isDragging}
                    isOver={isOver}
                    attributes={attributes}
                    listeners={listeners}
                    innerRef={ref}
                />
            )}
        </DragDropWrapper>
    );
}, (prevProps, nextProps) => { // return false to trigger a re-render
    // Custom equality function to prevent unnecessary re-renders
    if (prevProps.isActive !== nextProps.isActive) return false;
    if (prevProps.isSelected !== nextProps.isSelected) {
        console.log("selected changed 000", nextProps.stackIndex, prevProps.isSelected, nextProps.isSelected);
        return false;
    }

    // if both itemStacks are the same object instance in memory, no need to compare
    if (prevProps.itemStack === nextProps.itemStack) return true;

    // both itemStacks are empty
    if (!prevProps.itemStack && !nextProps.itemStack) return true;
    // one of the itemStacks is not empty
    if (!prevProps.itemStack || !nextProps.itemStack) return false;

    // console.log("compare !!!", nextProps.itemStack?.itemDef?.name,
    //     prevProps.itemStack, nextProps.itemStack
    // );
    return (
        prevProps.itemStack.uuid === nextProps.itemStack.uuid &&
        prevProps.itemStack.quantity === nextProps.itemStack.quantity &&
        prevProps.itemStack.freshness === nextProps.itemStack.freshness && 
        prevProps.itemStack.itemId === nextProps.itemStack.itemId &&
        prevProps.itemStack.durability === nextProps.itemStack.durability
    );
});
